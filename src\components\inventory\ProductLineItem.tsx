import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { Trash2, Search, Package, AlertTriangle } from 'lucide-react'
import { useActiveProducts } from '@/hooks/queries'
import { cn } from '@/lib/utils'
import type { ProductWithCategory } from '@/types/inventory'

interface LineItemData {
  product_id?: string
  item: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number
}

interface ProductLineItemProps {
  line: LineItemData
  index: number
  onUpdate: (index: number, field: keyof LineItemData, value: string | number) => void
  onRemove: (index: number) => void
  isLast: boolean
  showProductSelection?: boolean
  className?: string
}

export function ProductLineItem({
  line,
  index,
  onUpdate,
  onRemove,
  isLast,
  showProductSelection = true,
  className
}: ProductLineItemProps) {
  const [productSearchOpen, setProductSearchOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<ProductWithCategory | null>(null)
  
  const { data: products = [] } = useActiveProducts()

  // Find selected product when product_id changes
  useEffect(() => {
    if (line.product_id) {
      const product = products.find(p => p.id === line.product_id)
      setSelectedProduct(product || null)
    } else {
      setSelectedProduct(null)
    }
  }, [line.product_id, products])

  const handleProductSelect = (product: ProductWithCategory) => {
    setSelectedProduct(product)
    setProductSearchOpen(false)
    
    // Update line item with product details
    onUpdate(index, 'product_id', product.id)
    onUpdate(index, 'item', product.name)
    onUpdate(index, 'description', product.description || '')
    onUpdate(index, 'unit_price', Number(product.selling_price) || 0)
  }

  const handleClearProduct = () => {
    setSelectedProduct(null)
    onUpdate(index, 'product_id', '')
  }

  const lineTotal = line.quantity * line.unit_price
  const taxAmount = lineTotal * (line.tax_rate_pct / 100)
  const totalWithTax = lineTotal + taxAmount

  return (
    <div className={cn("grid grid-cols-12 gap-2 items-start p-3 border rounded-lg", className)}>
      {/* Product Selection */}
      {showProductSelection && (
        <div className="col-span-3">
          <label className="text-xs font-medium text-gray-700 mb-1 block">
            Product
          </label>
          {selectedProduct ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2 p-2 bg-blue-50 border border-blue-200 rounded">
                <Package className="h-4 w-4 text-blue-600" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-blue-900 truncate">
                    {selectedProduct.name}
                  </div>
                  <div className="text-xs text-blue-600 truncate">
                    SKU: {selectedProduct.sku}
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClearProduct}
                  className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
                >
                  ×
                </Button>
              </div>
              {!selectedProduct.is_sellable && (
                <div className="flex items-center gap-1 text-xs text-orange-600">
                  <AlertTriangle className="h-3 w-3" />
                  Not marked as sellable
                </div>
              )}
            </div>
          ) : (
            <Popover open={productSearchOpen} onOpenChange={setProductSearchOpen}>
              <PopoverTrigger asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Select product...
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="start">
                <Command>
                  <CommandInput placeholder="Search products..." />
                  <CommandEmpty>No products found.</CommandEmpty>
                  <CommandGroup className="max-h-64 overflow-auto">
                    {products
                      .filter(product => product.is_sellable)
                      .map((product) => (
                        <CommandItem
                          key={product.id}
                          onSelect={() => handleProductSelect(product)}
                          className="flex items-center gap-2 p-2"
                        >
                          <Package className="h-4 w-4" />
                          <div className="flex-1">
                            <div className="font-medium">{product.name}</div>
                            <div className="text-xs text-gray-500">
                              SKU: {product.sku} • ${product.selling_price?.toFixed(2) || '0.00'}
                            </div>
                            {product.category && (
                              <Badge variant="secondary" className="text-xs">
                                {product.category.name}
                              </Badge>
                            )}
                          </div>
                        </CommandItem>
                      ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
          )}
        </div>
      )}

      {/* Item Name */}
      <div className={showProductSelection ? "col-span-2" : "col-span-3"}>
        <label className="text-xs font-medium text-gray-700 mb-1 block">
          Item *
        </label>
        <Input
          value={line.item}
          onChange={(e) => onUpdate(index, 'item', e.target.value)}
          placeholder="Item name"
          className="text-sm"
        />
      </div>

      {/* Description */}
      <div className="col-span-2">
        <label className="text-xs font-medium text-gray-700 mb-1 block">
          Description
        </label>
        <Textarea
          value={line.description}
          onChange={(e) => onUpdate(index, 'description', e.target.value)}
          placeholder="Description"
          rows={1}
          className="text-sm resize-none"
        />
      </div>

      {/* Quantity */}
      <div className="col-span-1">
        <label className="text-xs font-medium text-gray-700 mb-1 block">
          Qty *
        </label>
        <Input
          type="number"
          step="0.001"
          min="0"
          value={line.quantity}
          onChange={(e) => onUpdate(index, 'quantity', parseFloat(e.target.value) || 0)}
          placeholder="0"
          className="text-sm"
        />
      </div>

      {/* Unit Price */}
      <div className="col-span-1">
        <label className="text-xs font-medium text-gray-700 mb-1 block">
          Price *
        </label>
        <Input
          type="number"
          step="0.01"
          min="0"
          value={line.unit_price}
          onChange={(e) => onUpdate(index, 'unit_price', parseFloat(e.target.value) || 0)}
          placeholder="0.00"
          className="text-sm"
        />
      </div>

      {/* Tax Rate */}
      <div className="col-span-1">
        <label className="text-xs font-medium text-gray-700 mb-1 block">
          Tax %
        </label>
        <Input
          type="number"
          step="0.01"
          min="0"
          max="100"
          value={line.tax_rate_pct}
          onChange={(e) => onUpdate(index, 'tax_rate_pct', parseFloat(e.target.value) || 0)}
          placeholder="0"
          className="text-sm"
        />
      </div>

      {/* Total */}
      <div className="col-span-1">
        <label className="text-xs font-medium text-gray-700 mb-1 block">
          Total
        </label>
        <div className="text-sm font-medium text-gray-900 py-2">
          ${totalWithTax.toFixed(2)}
        </div>
        {taxAmount > 0 && (
          <div className="text-xs text-gray-500">
            +${taxAmount.toFixed(2)} tax
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="col-span-1 flex justify-end">
        <label className="text-xs font-medium text-gray-700 mb-1 block invisible">
          Actions
        </label>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => onRemove(index)}
          className="text-red-600 hover:text-red-800 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      {/* Stock Warning */}
      {selectedProduct && selectedProduct.track_inventory && (
        <div className="col-span-12 mt-2">
          {selectedProduct.is_low_stock && (
            <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 p-2 rounded">
              <AlertTriangle className="h-3 w-3" />
              Low stock warning: Only {selectedProduct.total_quantity_available || 0} units available
            </div>
          )}
          {(selectedProduct.total_quantity_available || 0) < line.quantity && (
            <div className="flex items-center gap-2 text-xs text-red-600 bg-red-50 p-2 rounded">
              <AlertTriangle className="h-3 w-3" />
              Insufficient stock: Requested {line.quantity}, available {selectedProduct.total_quantity_available || 0}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
