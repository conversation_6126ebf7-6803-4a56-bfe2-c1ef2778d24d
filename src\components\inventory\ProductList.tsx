import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Eye, 
  Trash2, 
  Package,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { useProductsWithStock, useToggleProductStatus, useDeleteProduct } from '@/hooks/queries'
import { useDebounce } from '@/hooks/use-debounce'
import type { ProductWithStock, ProductFilters } from '@/types/inventory'

interface ProductListProps {
  onCreateProduct: () => void
  onEditProduct: (product: ProductWithStock) => void
  onViewProduct: (product: ProductWithStock) => void
}

export function ProductList({ onCreateProduct, onEditProduct, onViewProduct }: ProductListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<ProductFilters>({})
  
  const debouncedSearch = useDebounce(searchTerm, 300)
  
  const { data: products = [], isLoading, error } = useProductsWithStock()
  const toggleStatus = useToggleProductStatus()
  const deleteProduct = useDeleteProduct()

  // Filter products based on search and filters
  const filteredProducts = products.filter(product => {
    if (debouncedSearch) {
      const searchLower = debouncedSearch.toLowerCase()
      const matchesSearch = 
        product.name.toLowerCase().includes(searchLower) ||
        product.sku.toLowerCase().includes(searchLower) ||
        (product.description && product.description.toLowerCase().includes(searchLower))
      
      if (!matchesSearch) return false
    }

    if (filters.is_active !== undefined && product.is_active !== filters.is_active) {
      return false
    }

    if (filters.low_stock && !product.is_low_stock) {
      return false
    }

    if (filters.out_of_stock && (product.total_quantity_available || 0) > 0) {
      return false
    }

    return true
  })

  const handleToggleStatus = async (product: ProductWithStock) => {
    try {
      await toggleStatus.mutateAsync({
        productId: product.id,
        isActive: !product.is_active
      })
    } catch (error) {
      console.error('Failed to toggle product status:', error)
    }
  }

  const handleDeleteProduct = async (product: ProductWithStock) => {
    if (window.confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
      try {
        await deleteProduct.mutateAsync(product.id)
      } catch (error) {
        console.error('Failed to delete product:', error)
      }
    }
  }

  const getStockStatusBadge = (product: ProductWithStock) => {
    if (!product.track_inventory) {
      return <Badge variant="secondary">Not Tracked</Badge>
    }

    const available = product.total_quantity_available || 0
    
    if (available <= 0) {
      return <Badge variant="destructive">Out of Stock</Badge>
    }
    
    if (product.is_low_stock) {
      return <Badge variant="outline" className="border-orange-500 text-orange-600">Low Stock</Badge>
    }
    
    return <Badge variant="default" className="bg-green-100 text-green-800">In Stock</Badge>
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            Failed to load products. Please try again.
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Products
            </CardTitle>
            <CardDescription>
              Manage your product catalog and inventory
            </CardDescription>
          </div>
          <Button onClick={onCreateProduct}>
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search products by name, SKU, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={filters.is_active === true ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  is_active: prev.is_active === true ? undefined : true 
                }))}
              >
                Active Only
              </Button>
              <Button
                variant={filters.low_stock ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  low_stock: !prev.low_stock 
                }))}
              >
                Low Stock
              </Button>
              <Button
                variant={filters.out_of_stock ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  out_of_stock: !prev.out_of_stock 
                }))}
              >
                Out of Stock
              </Button>
            </div>
          </div>

          {/* Products Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Stock Status</TableHead>
                  <TableHead>On Hand</TableHead>
                  <TableHead>Available</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      Loading products...
                    </TableCell>
                  </TableRow>
                ) : filteredProducts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      {debouncedSearch || Object.keys(filters).length > 0 
                        ? 'No products match your search criteria.' 
                        : 'No products found. Create your first product to get started.'
                      }
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id} className="cursor-pointer hover:bg-gray-50">
                      <TableCell>
                        <div>
                          <div className="font-medium">{product.name}</div>
                          {product.description && (
                            <div className="text-sm text-gray-500 truncate max-w-[200px]">
                              {product.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                      <TableCell>
                        {product.category?.name || (
                          <span className="text-gray-400">Uncategorized</span>
                        )}
                      </TableCell>
                      <TableCell>{getStockStatusBadge(product)}</TableCell>
                      <TableCell>
                        {product.track_inventory ? (
                          <span className="font-mono">
                            {product.total_quantity_on_hand || 0} {product.unit_of_measure}
                          </span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {product.track_inventory ? (
                          <span className="font-mono">
                            {product.total_quantity_available || 0} {product.unit_of_measure}
                          </span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        ${product.selling_price?.toFixed(2) || '0.00'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {product.is_active ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )}
                          <span className={product.is_active ? 'text-green-600' : 'text-red-600'}>
                            {product.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => onViewProduct(product)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onEditProduct(product)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Product
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleToggleStatus(product)}>
                              {product.is_active ? (
                                <>
                                  <XCircle className="mr-2 h-4 w-4" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteProduct(product)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Summary */}
          {filteredProducts.length > 0 && (
            <div className="text-sm text-gray-500">
              Showing {filteredProducts.length} of {products.length} products
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
