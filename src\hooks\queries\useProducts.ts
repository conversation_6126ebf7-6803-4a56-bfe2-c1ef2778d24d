import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import type { 
  Product, 
  ProductWithCategory, 
  ProductWithStock, 
  ProductFormData,
  ProductFilters 
} from '@/types/inventory'

/**
 * Hook to fetch all products for an organization
 */
export function useProducts(filters?: ProductFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.products.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('products')
        .select(`
          *,
          category:product_categories(*)
        `)
        .eq('org_id', profile.org_id)
        .order('name')

      // Apply filters
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,sku.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }
      if (filters?.category_id) {
        query = query.eq('category_id', filters.category_id)
      }
      if (filters?.is_active !== undefined) {
        query = query.eq('is_active', filters.is_active)
      }
      if (filters?.is_sellable !== undefined) {
        query = query.eq('is_sellable', filters.is_sellable)
      }
      if (filters?.is_purchasable !== undefined) {
        query = query.eq('is_purchasable', filters.is_purchasable)
      }
      if (filters?.track_inventory !== undefined) {
        query = query.eq('track_inventory', filters.track_inventory)
      }

      const { data, error } = await query

      if (error) throw error
      return data as ProductWithCategory[]
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch active products only
 */
export function useActiveProducts() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.products.active(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:product_categories(*)
        `)
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('name')

      if (error) throw error
      return data as ProductWithCategory[]
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch products with stock information
 */
export function useProductsWithStock() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.products.withStock(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:product_categories(*),
          stock_levels(
            *,
            location:inventory_locations(*)
          )
        `)
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('name')

      if (error) throw error
      
      // Calculate totals and low stock status
      const productsWithStock = data.map(product => {
        const stockLevels = product.stock_levels || []
        const total_quantity_on_hand = stockLevels.reduce((sum, stock) => sum + (stock.quantity_on_hand || 0), 0)
        const total_quantity_available = stockLevels.reduce((sum, stock) => sum + (stock.quantity_available || 0), 0)
        const is_low_stock = product.track_inventory && total_quantity_available <= (product.reorder_level || 0)

        return {
          ...product,
          total_quantity_on_hand,
          total_quantity_available,
          is_low_stock
        }
      })

      return productsWithStock as ProductWithStock[]
    },
    enabled: !!profile?.org_id,
    staleTime: 2 * 60 * 1000, // 2 minutes (stock data changes frequently)
  })
}

/**
 * Hook to fetch low stock products
 */
export function useLowStockProducts() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.products.lowStock(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      // This query finds products where total available stock is at or below reorder level
      const { data, error } = await supabase.rpc('get_low_stock_products', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data as ProductWithStock[]
    },
    enabled: !!profile?.org_id,
    staleTime: 1 * 60 * 1000, // 1 minute (critical data)
  })
}

/**
 * Hook to fetch a single product by ID
 */
export function useProduct(productId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.products.detail(profile?.org_id || '', productId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !productId) return null

      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:product_categories(*),
          stock_levels(
            *,
            location:inventory_locations(*)
          )
        `)
        .eq('id', productId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data as ProductWithStock
    },
    enabled: !!profile?.org_id && !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to create a new product
 */
export function useCreateProduct() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (productData: ProductFormData) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('products')
        .insert({
          ...productData,
          org_id: profile.org_id,
          created_by: profile.id,
        })
        .select()
        .single()

      if (error) throw error
      return data as Product
    },
    onSuccess: () => {
      // Invalidate all product-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.withStock(profile?.org_id || '') })
    },
  })
}

/**
 * Hook to update a product
 */
export function useUpdateProduct() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ productId, productData }: { productId: string; productData: Partial<ProductFormData> }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('products')
        .update({
          ...productData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', productId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data as Product
    },
    onSuccess: (data) => {
      // Invalidate all product-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.withStock(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.detail(profile?.org_id || '', data.id) })
    },
  })
}

/**
 * Hook to delete a product
 */
export function useDeleteProduct() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (productId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId)
        .eq('org_id', profile.org_id)

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate all product-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.withStock(profile?.org_id || '') })
    },
  })
}

/**
 * Hook to toggle product active status
 */
export function useToggleProductStatus() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ productId, isActive }: { productId: string; isActive: boolean }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('products')
        .update({ 
          is_active: isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('id', productId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data as Product
    },
    onSuccess: (data) => {
      // Invalidate all product-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.detail(profile?.org_id || '', data.id) })
    },
  })
}
