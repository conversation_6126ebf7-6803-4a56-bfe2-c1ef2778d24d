import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import type { 
  StockLevel, 
  StockLevelWithProduct, 
  StockLevelWithLocation,
  StockAdjustmentFormData 
} from '@/types/inventory'

/**
 * Hook to fetch all stock levels for an organization
 */
export function useStockLevels() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.stockLevels.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('stock_levels')
        .select(`
          *,
          product:products(*),
          location:inventory_locations(*)
        `)
        .eq('org_id', profile.org_id)
        .order('last_updated', { ascending: false })

      if (error) throw error
      return data as StockLevelWithProduct[]
    },
    enabled: !!profile?.org_id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to fetch stock levels for a specific product
 */
export function useStockLevelsByProduct(productId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.stockLevels.byProduct(profile?.org_id || '', productId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !productId) return []

      const { data, error } = await supabase
        .from('stock_levels')
        .select(`
          *,
          location:inventory_locations(*)
        `)
        .eq('org_id', profile.org_id)
        .eq('product_id', productId)
        .order('location.name')

      if (error) throw error
      return data as StockLevelWithLocation[]
    },
    enabled: !!profile?.org_id && !!productId,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

/**
 * Hook to fetch stock levels for a specific location
 */
export function useStockLevelsByLocation(locationId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.stockLevels.byLocation(profile?.org_id || '', locationId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !locationId) return []

      const { data, error } = await supabase
        .from('stock_levels')
        .select(`
          *,
          product:products(*)
        `)
        .eq('org_id', profile.org_id)
        .eq('location_id', locationId)
        .order('product.name')

      if (error) throw error
      return data as (StockLevel & { product: any })[]
    },
    enabled: !!profile?.org_id && !!locationId,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

/**
 * Hook to fetch low stock levels
 */
export function useLowStockLevels() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.stockLevels.lowStock(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      // Get stock levels where available quantity is at or below reorder level
      const { data, error } = await supabase
        .from('stock_levels')
        .select(`
          *,
          product:products(*),
          location:inventory_locations(*)
        `)
        .eq('org_id', profile.org_id)
        .filter('product.track_inventory', 'eq', true)
        .order('quantity_available', { ascending: true })

      if (error) throw error

      // Filter for low stock items
      const lowStockItems = data.filter(item => {
        const reorderLevel = item.product?.reorder_level || 0
        return (item.quantity_available || 0) <= reorderLevel
      })

      return lowStockItems as StockLevelWithProduct[]
    },
    enabled: !!profile?.org_id,
    staleTime: 30 * 1000, // 30 seconds (critical data)
  })
}

/**
 * Hook to get stock level for a specific product at a specific location
 */
export function useStockLevel(productId: string | undefined, locationId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['stock-level', profile?.org_id, productId, locationId],
    queryFn: async () => {
      if (!profile?.org_id || !productId || !locationId) return null

      const { data, error } = await supabase
        .from('stock_levels')
        .select(`
          *,
          product:products(*),
          location:inventory_locations(*)
        `)
        .eq('org_id', profile.org_id)
        .eq('product_id', productId)
        .eq('location_id', locationId)
        .single()

      if (error) {
        // If no stock level exists, return a default one
        if (error.code === 'PGRST116') {
          return {
            id: '',
            org_id: profile.org_id,
            product_id: productId,
            location_id: locationId,
            quantity_on_hand: 0,
            quantity_reserved: 0,
            quantity_available: 0,
            average_cost: 0,
            last_cost: 0,
            last_updated: new Date().toISOString(),
          } as StockLevel
        }
        throw error
      }

      return data as StockLevelWithProduct
    },
    enabled: !!profile?.org_id && !!productId && !!locationId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

/**
 * Hook to perform stock adjustment
 */
export function useStockAdjustment() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (adjustmentData: StockAdjustmentFormData) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Call the stock adjustment function
      const { data, error } = await supabase.rpc('adjust_stock_level', {
        p_org_id: profile.org_id,
        p_product_id: adjustmentData.product_id,
        p_location_id: adjustmentData.location_id,
        p_adjustment_type: adjustmentData.adjustment_type,
        p_quantity: adjustmentData.quantity,
        p_reason_code: adjustmentData.reason_code,
        p_notes: adjustmentData.notes,
        p_unit_cost: adjustmentData.unit_cost,
        p_user_id: profile.id
      })

      if (error) throw error
      return data
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.byProduct(profile?.org_id || '', variables.product_id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.byLocation(profile?.org_id || '', variables.location_id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.lowStock(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.byProduct(profile?.org_id || '', variables.product_id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.withStock(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lowStock(profile?.org_id || '') })
    },
  })
}

/**
 * Hook to reserve stock for orders
 */
export function useReserveStock() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ productId, locationId, quantity, referenceType, referenceId }: {
      productId: string
      locationId: string
      quantity: number
      referenceType: string
      referenceId: string
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase.rpc('reserve_stock', {
        p_org_id: profile.org_id,
        p_product_id: productId,
        p_location_id: locationId,
        p_quantity: quantity,
        p_reference_type: referenceType,
        p_reference_id: referenceId,
        p_user_id: profile.id
      })

      if (error) throw error
      return data
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.byProduct(profile?.org_id || '', variables.productId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.byLocation(profile?.org_id || '', variables.locationId) })
      queryClient.invalidateQueries({ queryKey: ['stock-level', profile?.org_id, variables.productId, variables.locationId] })
    },
  })
}

/**
 * Hook to release reserved stock
 */
export function useReleaseStock() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ productId, locationId, quantity, referenceType, referenceId }: {
      productId: string
      locationId: string
      quantity: number
      referenceType: string
      referenceId: string
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase.rpc('release_stock', {
        p_org_id: profile.org_id,
        p_product_id: productId,
        p_location_id: locationId,
        p_quantity: quantity,
        p_reference_type: referenceType,
        p_reference_id: referenceId,
        p_user_id: profile.id
      })

      if (error) throw error
      return data
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.byProduct(profile?.org_id || '', variables.productId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.byLocation(profile?.org_id || '', variables.locationId) })
      queryClient.invalidateQueries({ queryKey: ['stock-level', profile?.org_id, variables.productId, variables.locationId] })
    },
  })
}
